import '../models/recipe.dart';

/// Service for calculating scaled ingredient quantities based on target volume
class RecipeCalculatorService {
  /// Calculate scaled ingredients for a target volume
  /// 
  /// [recipe] - The base recipe to scale
  /// [targetVolume] - The desired final volume in liters
  /// [baseVolume] - The base volume the recipe is designed for (default: 1L)
  /// 
  /// Returns a map of ingredient names to their calculated amounts
  static Map<String, CalculatedIngredient> calculateIngredients({
    required Recipe recipe,
    required double targetVolume,
    double baseVolume = 1.0,
  }) {
    if (targetVolume <= 0 || baseVolume <= 0) {
      throw ArgumentError('Volume must be greater than 0');
    }

    final scaleFactor = targetVolume / baseVolume;
    final calculatedIngredients = <String, CalculatedIngredient>{};

    for (final entry in recipe.ingredients.entries) {
      final ingredientName = entry.key;
      final ingredient = entry.value;
      
      calculatedIngredients[ingredientName] = CalculatedIngredient(
        originalAmount: ingredient.amount,
        originalUnit: ingredient.unit,
        scaledAmount: _calculateScaledAmount(ingredient.amount, scaleFactor),
        scaledUnit: ingredient.unit,
        scaleFactor: scaleFactor,
        notes: ingredient.notes,
      );
    }

    return calculatedIngredients;
  }

  /// Calculate the scaled amount with appropriate precision
  static double _calculateScaledAmount(double originalAmount, double scaleFactor) {
    final scaledAmount = originalAmount * scaleFactor;
    
    // Round to appropriate precision based on magnitude
    if (scaledAmount >= 100) {
      return double.parse(scaledAmount.toStringAsFixed(1));
    } else if (scaledAmount >= 10) {
      return double.parse(scaledAmount.toStringAsFixed(2));
    } else if (scaledAmount >= 1) {
      return double.parse(scaledAmount.toStringAsFixed(3));
    } else {
      return double.parse(scaledAmount.toStringAsFixed(4));
    }
  }

  /// Get common volume presets for quick selection
  static List<VolumePreset> getVolumePresets() {
    return [
      VolumePreset(name: '100 mL', volume: 0.1),
      VolumePreset(name: '250 mL', volume: 0.25),
      VolumePreset(name: '500 mL', volume: 0.5),
      VolumePreset(name: '1 L', volume: 1.0),
      VolumePreset(name: '2 L', volume: 2.0),
      VolumePreset(name: '5 L', volume: 5.0),
      VolumePreset(name: '10 L', volume: 10.0),
    ];
  }

  /// Calculate total cost if ingredient costs are provided
  static double calculateTotalCost(
    Map<String, CalculatedIngredient> calculatedIngredients,
    Map<String, double> ingredientCosts,
  ) {
    double totalCost = 0.0;
    
    for (final entry in calculatedIngredients.entries) {
      final ingredientName = entry.key;
      final calculatedIngredient = entry.value;
      final costPerUnit = ingredientCosts[ingredientName];
      
      if (costPerUnit != null) {
        totalCost += calculatedIngredient.scaledAmount * costPerUnit;
      }
    }
    
    return double.parse(totalCost.toStringAsFixed(2));
  }
}

/// Represents a calculated ingredient with original and scaled values
class CalculatedIngredient {
  final double originalAmount;
  final String originalUnit;
  final double scaledAmount;
  final String scaledUnit;
  final double scaleFactor;
  final String? notes;

  CalculatedIngredient({
    required this.originalAmount,
    required this.originalUnit,
    required this.scaledAmount,
    required this.scaledUnit,
    required this.scaleFactor,
    this.notes,
  });

  /// Get formatted original amount with unit
  String get formattedOriginalAmount {
    if (originalAmount == originalAmount.toInt()) {
      return '${originalAmount.toInt()} $originalUnit';
    }
    return '$originalAmount $originalUnit';
  }

  /// Get formatted scaled amount with unit
  String get formattedScaledAmount {
    if (scaledAmount == scaledAmount.toInt()) {
      return '${scaledAmount.toInt()} $scaledUnit';
    }
    return '$scaledAmount $scaledUnit';
  }

  /// Get the percentage change from original
  double get percentageChange {
    return (scaleFactor - 1) * 100;
  }

  @override
  String toString() {
    return 'CalculatedIngredient(original: $formattedOriginalAmount, scaled: $formattedScaledAmount)';
  }
}

/// Represents a volume preset for quick selection
class VolumePreset {
  final String name;
  final double volume;

  VolumePreset({required this.name, required this.volume});

  @override
  String toString() => name;
}
