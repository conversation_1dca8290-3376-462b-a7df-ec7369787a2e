import 'package:flutter/material.dart';
import '../models/recipe.dart';

/// Widget for managing ingredients in recipe forms
class IngredientFormWidget extends StatefulWidget {
  final Map<String, Ingredient> ingredients;
  final Function(Map<String, Ingredient>) onIngredientsChanged;

  const IngredientFormWidget({
    super.key,
    required this.ingredients,
    required this.onIngredientsChanged,
  });

  @override
  State<IngredientFormWidget> createState() => _IngredientFormWidgetState();
}

class _IngredientFormWidgetState extends State<IngredientFormWidget> {
  final _nameController = TextEditingController();
  final _amountController = TextEditingController();
  final _unitController = TextEditingController();
  final _notesController = TextEditingController();

  @override
  void dispose() {
    _nameController.dispose();
    _amountController.dispose();
    _unitController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Text(
              'Ingredients',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const Spacer(),
            TextButton.icon(
              onPressed: _showAddIngredientDialog,
              icon: const Icon(Icons.add),
              label: const Text('Add Ingredient'),
            ),
          ],
        ),
        const SizedBox(height: 8),
        
        if (widget.ingredients.isEmpty)
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Center(
              child: Text(
                'No ingredients added yet.\nTap "Add Ingredient" to get started.',
                textAlign: TextAlign.center,
                style: TextStyle(color: Colors.grey),
              ),
            ),
          )
        else
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: widget.ingredients.entries.map((entry) {
                return _buildIngredientTile(entry.key, entry.value);
              }).toList(),
            ),
          ),
      ],
    );
  }

  Widget _buildIngredientTile(String name, Ingredient ingredient) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade200),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 16,
                  ),
                ),
                Text(
                  ingredient.formattedAmount,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 14,
                  ),
                ),
                if (ingredient.notes != null && ingredient.notes!.isNotEmpty)
                  Text(
                    ingredient.notes!,
                    style: TextStyle(
                      color: Colors.grey[500],
                      fontSize: 12,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => _editIngredient(name, ingredient),
            icon: const Icon(Icons.edit, size: 20),
            tooltip: 'Edit ingredient',
          ),
          IconButton(
            onPressed: () => _removeIngredient(name),
            icon: const Icon(Icons.delete, size: 20, color: Colors.red),
            tooltip: 'Remove ingredient',
          ),
        ],
      ),
    );
  }

  void _showAddIngredientDialog() {
    _clearControllers();
    _showIngredientDialog('Add Ingredient', null);
  }

  void _editIngredient(String name, Ingredient ingredient) {
    _nameController.text = name;
    _amountController.text = ingredient.amount.toString();
    _unitController.text = ingredient.unit;
    _notesController.text = ingredient.notes ?? '';
    _showIngredientDialog('Edit Ingredient', name);
  }

  void _showIngredientDialog(String title, String? existingName) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'Ingredient Name',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: TextField(
                      controller: _amountController,
                      decoration: const InputDecoration(
                        labelText: 'Amount',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: TextField(
                      controller: _unitController,
                      decoration: const InputDecoration(
                        labelText: 'Unit',
                        border: OutlineInputBorder(),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              TextField(
                controller: _notesController,
                decoration: const InputDecoration(
                  labelText: 'Notes (optional)',
                  border: OutlineInputBorder(),
                ),
                maxLines: 2,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => _saveIngredient(existingName),
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  void _saveIngredient(String? existingName) {
    final name = _nameController.text.trim();
    final amountText = _amountController.text.trim();
    final unit = _unitController.text.trim();
    final notes = _notesController.text.trim();

    if (name.isEmpty || amountText.isEmpty || unit.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please fill in all required fields'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final amount = double.tryParse(amountText);
    if (amount == null || amount <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter a valid amount'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final ingredient = Ingredient(
      amount: amount,
      unit: unit,
      notes: notes.isEmpty ? null : notes,
    );

    final updatedIngredients = Map<String, Ingredient>.from(widget.ingredients);
    
    // Remove old ingredient if editing
    if (existingName != null && existingName != name) {
      updatedIngredients.remove(existingName);
    }
    
    updatedIngredients[name] = ingredient;
    widget.onIngredientsChanged(updatedIngredients);
    
    Navigator.pop(context);
    _clearControllers();
  }

  void _removeIngredient(String name) {
    final updatedIngredients = Map<String, Ingredient>.from(widget.ingredients);
    updatedIngredients.remove(name);
    widget.onIngredientsChanged(updatedIngredients);
  }

  void _clearControllers() {
    _nameController.clear();
    _amountController.clear();
    _unitController.clear();
    _notesController.clear();
  }
}
