import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/recipe_provider.dart';
import '../models/recipe.dart';
import '../services/recipe_calculator_service.dart';
import '../widgets/calculator_result_widget.dart';

class RecipeCalculatorScreen extends StatefulWidget {
  final int recipeId;

  const RecipeCalculatorScreen({super.key, required this.recipeId});

  @override
  State<RecipeCalculatorScreen> createState() => _RecipeCalculatorScreenState();
}

class _RecipeCalculatorScreenState extends State<RecipeCalculatorScreen> {
  final _volumeController = TextEditingController();
  final _baseVolumeController = TextEditingController(text: '1.0');
  
  Recipe? recipe;
  bool isLoading = true;
  double? targetVolume;
  double baseVolume = 1.0;
  Map<String, CalculatedIngredient>? calculatedIngredients;

  @override
  void initState() {
    super.initState();
    _loadRecipe();
  }

  @override
  void dispose() {
    _volumeController.dispose();
    _baseVolumeController.dispose();
    super.dispose();
  }

  void _loadRecipe() {
    final recipeProvider = context.read<RecipeProvider>();
    recipe = recipeProvider.getRecipeById(widget.recipeId);
    
    if (recipe == null) {
      recipeProvider.loadRecipes().then((_) {
        if (mounted) {
          setState(() {
            recipe = recipeProvider.getRecipeById(widget.recipeId);
            isLoading = false;
          });
        }
      });
    } else {
      setState(() {
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Recipe Calculator'),
          backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        ),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    if (recipe == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Recipe Calculator'),
          backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        ),
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error_outline, size: 64, color: Colors.red),
              SizedBox(height: 16),
              Text('Recipe not found'),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Recipe Calculator'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Recipe info card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  children: [
                    Icon(
                      recipe!.isTemplate ? Icons.star : Icons.menu_book,
                      color: Theme.of(context).colorScheme.primary,
                      size: 32,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            recipe!.name,
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            recipe!.mediaType.toUpperCase(),
                            style: TextStyle(
                              color: Theme.of(context).colorScheme.primary,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          Text(
                            '${recipe!.ingredientCount} ingredients',
                            style: const TextStyle(color: Colors.grey),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),

            // Volume input section
            Text(
              'Volume Configuration',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // Base volume input
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _baseVolumeController,
                    decoration: const InputDecoration(
                      labelText: 'Base Volume (L)',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.science),
                      helperText: 'Volume the recipe is designed for',
                    ),
                    keyboardType: TextInputType.number,
                    onChanged: (value) {
                      final volume = double.tryParse(value);
                      if (volume != null && volume > 0) {
                        setState(() {
                          baseVolume = volume;
                          _calculateIngredients();
                        });
                      }
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Target volume input
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _volumeController,
                    decoration: const InputDecoration(
                      labelText: 'Target Volume (L)',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.straighten),
                      helperText: 'Desired final volume',
                    ),
                    keyboardType: TextInputType.number,
                    onChanged: (value) {
                      final volume = double.tryParse(value);
                      if (volume != null && volume > 0) {
                        setState(() {
                          targetVolume = volume;
                          _calculateIngredients();
                        });
                      } else {
                        setState(() {
                          targetVolume = null;
                          calculatedIngredients = null;
                        });
                      }
                    },
                  ),
                ),
                const SizedBox(width: 16),
                ElevatedButton(
                  onPressed: _showVolumePresets,
                  child: const Text('Presets'),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Results section
            if (calculatedIngredients != null && targetVolume != null)
              CalculatorResultWidget(
                calculatedIngredients: calculatedIngredients!,
                targetVolume: targetVolume!,
                scaleFactor: targetVolume! / baseVolume,
              ),
          ],
        ),
      ),
    );
  }

  void _calculateIngredients() {
    if (targetVolume == null || recipe == null) {
      setState(() {
        calculatedIngredients = null;
      });
      return;
    }

    try {
      final calculated = RecipeCalculatorService.calculateIngredients(
        recipe: recipe!,
        targetVolume: targetVolume!,
        baseVolume: baseVolume,
      );
      
      setState(() {
        calculatedIngredients = calculated;
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error calculating ingredients: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _showVolumePresets() {
    final presets = RecipeCalculatorService.getVolumePresets();
    
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Volume Presets',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: presets.map((preset) {
                return ElevatedButton(
                  onPressed: () {
                    _volumeController.text = preset.volume.toString();
                    setState(() {
                      targetVolume = preset.volume;
                      _calculateIngredients();
                    });
                    Navigator.pop(context);
                  },
                  child: Text(preset.name),
                );
              }).toList(),
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }
}
