import 'package:flutter_test/flutter_test.dart';
import 'package:culture_track/models/culture.dart';

void main() {
  group('Culture Model Tests', () {
    late Culture testCulture;

    setUp(() {
      testCulture = Culture(
        species: 'Test Species',
        variety: 'Test Variety',
        source: 'Test Source',
        creationDate: DateTime(2024, 1, 1),
        currentStage: CultureStage.initiation,
        status: CultureStatus.active,
        photos: ['/test/photo1.jpg', '/test/photo2.jpg'],
      );
    });

    group('Photo Management', () {
      test('should initialize with empty photos list by default', () {
        final culture = Culture(
          species: 'Test Species',
          creationDate: DateTime.now(),
          currentStage: CultureStage.initiation,
          status: CultureStatus.active,
        );
        expect(culture.photos, isEmpty);
      });

      test('should initialize with provided photos list', () {
        expect(testCulture.photos.length, 2);
        expect(testCulture.photos, contains('/test/photo1.jpg'));
        expect(testCulture.photos, contains('/test/photo2.jpg'));
      });

      test('should create copy with updated photos', () {
        final newPhotos = ['/test/photo1.jpg', '/test/photo2.jpg', '/test/photo3.jpg'];
        final updatedCulture = testCulture.copyWith(photos: newPhotos);
        
        expect(updatedCulture.photos.length, 3);
        expect(updatedCulture.photos, contains('/test/photo3.jpg'));
        expect(testCulture.photos.length, 2); // Original should be unchanged
      });

      test('should serialize photos to JSON in toMap', () {
        final map = testCulture.toMap();
        expect(map['photos'], isA<String>());
        
        // The photos should be encoded as JSON string
        final photosJson = map['photos'] as String;
        expect(photosJson, contains('photo1.jpg'));
        expect(photosJson, contains('photo2.jpg'));
      });

      test('should deserialize photos from JSON in fromMap', () {
        final map = testCulture.toMap();
        final deserializedCulture = Culture.fromMap(map);
        
        expect(deserializedCulture.photos.length, 2);
        expect(deserializedCulture.photos, contains('/test/photo1.jpg'));
        expect(deserializedCulture.photos, contains('/test/photo2.jpg'));
      });

      test('should handle empty photos list in serialization', () {
        final cultureWithoutPhotos = testCulture.copyWith(photos: []);
        final map = cultureWithoutPhotos.toMap();
        final deserializedCulture = Culture.fromMap(map);
        
        expect(deserializedCulture.photos, isEmpty);
      });
    });

    group('Culture Status and Stage', () {
      test('should have correct display names', () {
        expect(CultureStatus.active.displayName, 'Active');
        expect(CultureStatus.contaminated.displayName, 'Contaminated');
        expect(CultureStage.initiation.displayName, 'Initiation');
        expect(CultureStage.multiplication.displayName, 'Multiplication');
      });

      test('should parse status from string', () {
        expect(CultureStatus.fromString('active'), CultureStatus.active);
        expect(CultureStatus.fromString('contaminated'), CultureStatus.contaminated);
        expect(CultureStatus.fromString('invalid'), CultureStatus.active); // Default fallback
      });

      test('should parse stage from string', () {
        expect(CultureStage.fromString('initiation'), CultureStage.initiation);
        expect(CultureStage.fromString('multiplication'), CultureStage.multiplication);
        expect(CultureStage.fromString('invalid'), CultureStage.initiation); // Default fallback
      });
    });

    group('Contamination Events', () {
      test('should initialize with empty contamination log by default', () {
        final culture = Culture(
          species: 'Test Species',
          creationDate: DateTime.now(),
          currentStage: CultureStage.initiation,
          status: CultureStatus.active,
        );
        expect(culture.contaminationLog, isEmpty);
      });

      test('should handle contamination events in serialization', () {
        final contaminationEvent = ContaminationEvent(
          date: DateTime(2024, 1, 15),
          cause: 'Bacterial contamination',
          notes: 'Observed during routine check',
        );
        
        final cultureWithContamination = testCulture.copyWith(
          contaminationLog: [contaminationEvent],
        );
        
        final map = cultureWithContamination.toMap();
        final deserializedCulture = Culture.fromMap(map);
        
        expect(deserializedCulture.contaminationLog.length, 1);
        expect(deserializedCulture.contaminationLog.first.cause, 'Bacterial contamination');
      });
    });
  });
}
