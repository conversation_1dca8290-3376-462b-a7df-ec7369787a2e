import 'package:flutter_test/flutter_test.dart';
import 'package:culture_track/models/recipe.dart';
import 'package:culture_track/services/recipe_calculator_service.dart';

void main() {
  group('RecipeCalculatorService', () {
    late Recipe testRecipe;

    setUp(() {
      testRecipe = Recipe(
        id: 1,
        name: 'Test MS Medium',
        mediaType: 'initiation',
        ingredients: {
          'MS Salts': Ingredient(amount: 4.4, unit: 'g/L'),
          'Sucrose': Ingredient(amount: 30, unit: 'g/L'),
          'Agar': Ingredient(amount: 8, unit: 'g/L'),
        },
      );
    });

    test('should calculate ingredients for 2L target volume', () {
      final result = RecipeCalculatorService.calculateIngredients(
        recipe: testRecipe,
        targetVolume: 2.0,
        baseVolume: 1.0,
      );

      expect(result.length, equals(3));
      expect(result['MS Salts']?.scaledAmount, equals(8.8));
      expect(result['Sucrose']?.scaledAmount, equals(60.0));
      expect(result['Agar']?.scaledAmount, equals(16.0));
      expect(result['MS Salts']?.scaleFactor, equals(2.0));
    });

    test('should calculate ingredients for 0.5L target volume', () {
      final result = RecipeCalculatorService.calculateIngredients(
        recipe: testRecipe,
        targetVolume: 0.5,
        baseVolume: 1.0,
      );

      expect(result.length, equals(3));
      expect(result['MS Salts']?.scaledAmount, equals(2.2));
      expect(result['Sucrose']?.scaledAmount, equals(15.0));
      expect(result['Agar']?.scaledAmount, equals(4.0));
      expect(result['MS Salts']?.scaleFactor, equals(0.5));
    });

    test('should handle different base volumes', () {
      final result = RecipeCalculatorService.calculateIngredients(
        recipe: testRecipe,
        targetVolume: 1.0,
        baseVolume: 2.0,
      );

      expect(result['MS Salts']?.scaledAmount, equals(2.2));
      expect(result['MS Salts']?.scaleFactor, equals(0.5));
    });

    test('should throw error for invalid volumes', () {
      expect(
        () => RecipeCalculatorService.calculateIngredients(
          recipe: testRecipe,
          targetVolume: 0,
          baseVolume: 1.0,
        ),
        throwsArgumentError,
      );

      expect(
        () => RecipeCalculatorService.calculateIngredients(
          recipe: testRecipe,
          targetVolume: 1.0,
          baseVolume: -1.0,
        ),
        throwsArgumentError,
      );
    });

    test('should provide volume presets', () {
      final presets = RecipeCalculatorService.getVolumePresets();
      
      expect(presets.isNotEmpty, isTrue);
      expect(presets.any((p) => p.name == '1 L'), isTrue);
      expect(presets.any((p) => p.volume == 0.5), isTrue);
    });

    test('CalculatedIngredient should format amounts correctly', () {
      final ingredient = CalculatedIngredient(
        originalAmount: 4.4,
        originalUnit: 'g/L',
        scaledAmount: 8.8,
        scaledUnit: 'g/L',
        scaleFactor: 2.0,
      );

      expect(ingredient.formattedOriginalAmount, equals('4.4 g/L'));
      expect(ingredient.formattedScaledAmount, equals('8.8 g/L'));
      expect(ingredient.percentageChange, equals(100.0));
    });

    test('CalculatedIngredient should handle integer amounts', () {
      final ingredient = CalculatedIngredient(
        originalAmount: 4.0,
        originalUnit: 'g/L',
        scaledAmount: 8.0,
        scaledUnit: 'g/L',
        scaleFactor: 2.0,
      );

      expect(ingredient.formattedOriginalAmount, equals('4 g/L'));
      expect(ingredient.formattedScaledAmount, equals('8 g/L'));
    });
  });
}
