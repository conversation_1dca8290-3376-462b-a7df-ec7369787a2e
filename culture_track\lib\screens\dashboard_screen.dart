import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../widgets/provider_demo_widget.dart';
import '../providers/culture_provider.dart';
import '../models/culture.dart';
import '../services/transfer_date_calculator.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Dashboard'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Welcome to CultureTrack',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              const Text(
                'Your tissue culture management dashboard',
                style: TextStyle(fontSize: 16, color: Colors.grey),
              ),
              const SizedBox(height: 32),
              // Dashboard widgets
              _DashboardStats(),
              const SizedBox(height: 16),
              _UpcomingTransfersCard(),
              const SizedBox(height: 16),
              _QuickActionsCard(),
              const SizedBox(height: 16),
              // Provider State Management Demo
              const ProviderDemoWidget(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _DashboardStats() {
    return Consumer<CultureProvider>(
      builder: (context, cultureProvider, child) {
        final activeCultures =
            cultureProvider.cultures
                .where((culture) => culture.status == CultureStatus.active)
                .length;

        final upcomingTransfers = _getUpcomingTransfersCount(
          cultureProvider.cultures,
        );

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Quick Stats',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    _StatItem(
                      icon: Icons.science,
                      label: 'Active Cultures',
                      value: activeCultures.toString(),
                      color: Colors.green,
                    ),
                    _StatItem(
                      icon: Icons.schedule,
                      label: 'Pending Transfers',
                      value: upcomingTransfers.toString(),
                      color: Colors.orange,
                    ),
                    _StatItem(
                      icon: Icons.menu_book,
                      label: 'Recipes',
                      value: '0', // TODO: Add recipe count when implemented
                      color: Colors.blue,
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  int _getUpcomingTransfersCount(List<Culture> cultures) {
    final now = DateTime.now();
    final thirtyDaysFromNow = now.add(const Duration(days: 30));

    return cultures.where((culture) {
      if (culture.nextTransferDate == null) return false;
      return culture.nextTransferDate!.isAfter(now) &&
          culture.nextTransferDate!.isBefore(thirtyDaysFromNow);
    }).length;
  }

  Widget _UpcomingTransfersCard() {
    return Consumer<CultureProvider>(
      builder: (context, cultureProvider, child) {
        final upcomingCultures = _getUpcomingCultures(cultureProvider.cultures);

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Upcoming Transfers',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    TextButton(
                      onPressed: () {
                        // Navigate to the main navigation and switch to transfers tab
                        Navigator.of(context).pushReplacementNamed('/');
                        // Note: We'll need to implement a way to switch to transfers tab
                        // For now, this will go to home and user can manually switch
                      },
                      child: const Text('View All'),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                if (upcomingCultures.isEmpty)
                  const Text(
                    'No upcoming transfers in the next 30 days',
                    style: TextStyle(color: Colors.grey),
                  )
                else
                  ...upcomingCultures
                      .take(3)
                      .map(
                        (culture) => _UpcomingTransferItem(culture: culture),
                      ),
              ],
            ),
          ),
        );
      },
    );
  }

  List<Culture> _getUpcomingCultures(List<Culture> cultures) {
    final now = DateTime.now();
    final thirtyDaysFromNow = now.add(const Duration(days: 30));

    final upcomingCultures =
        cultures.where((culture) {
          if (culture.nextTransferDate == null) return false;
          return culture.nextTransferDate!.isAfter(now) &&
              culture.nextTransferDate!.isBefore(thirtyDaysFromNow);
        }).toList();

    // Sort by transfer date (earliest first)
    upcomingCultures.sort(
      (a, b) => a.nextTransferDate!.compareTo(b.nextTransferDate!),
    );

    return upcomingCultures;
  }

  Widget _QuickActionsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Quick Actions',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _QuickActionButton(
                  icon: Icons.add,
                  label: 'Add Culture',
                  onPressed: () {
                    Navigator.of(context).pushNamed('/add-culture');
                  },
                ),
                _QuickActionButton(
                  icon: Icons.schedule,
                  label: 'View Transfers',
                  onPressed: () {
                    // Navigate to transfers tab (index 2)
                    // This is a workaround since we can't access the tab controller directly
                    Navigator.of(context).pushNamed('/');
                  },
                ),
                _QuickActionButton(
                  icon: Icons.menu_book,
                  label: 'Add Recipe',
                  onPressed: () {
                    Navigator.of(context).pushNamed('/add-recipe');
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _StatItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Column(
      children: [
        Icon(icon, color: color, size: 32),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
        ),
        Text(
          label,
          style: const TextStyle(fontSize: 12, color: Colors.grey),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _UpcomingTransferItem({required Culture culture}) {
    final dateCalculator = TransferDateCalculator();
    final urgency = dateCalculator.getTransferUrgency(
      culture.nextTransferDate!,
    );
    final daysUntil = dateCalculator.calculateDaysUntilTransfer(
      culture.nextTransferDate!,
    );

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  culture.species,
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
                if (culture.variety != null)
                  Text(
                    culture.variety!,
                    style: const TextStyle(fontSize: 12, color: Colors.grey),
                  ),
              ],
            ),
          ),
          Text(
            _getDaysUntilText(daysUntil),
            style: TextStyle(
              fontSize: 12,
              color: _getUrgencyColor(urgency),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _QuickActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
  }) {
    return Column(
      children: [
        ElevatedButton(
          onPressed: onPressed,
          style: ElevatedButton.styleFrom(
            shape: const CircleBorder(),
            padding: const EdgeInsets.all(16),
          ),
          child: Icon(icon),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: const TextStyle(fontSize: 12),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  String _getDaysUntilText(int days) {
    if (days < 0) {
      return '${days.abs()} days overdue';
    } else if (days == 0) {
      return 'Due today';
    } else if (days == 1) {
      return 'Due tomorrow';
    } else {
      return 'In $days days';
    }
  }

  Color _getUrgencyColor(TransferUrgency urgency) {
    switch (urgency) {
      case TransferUrgency.overdue:
        return Colors.red;
      case TransferUrgency.urgent:
        return Colors.orange;
      case TransferUrgency.soon:
        return Colors.amber;
      case TransferUrgency.upcoming:
        return Colors.blue;
      case TransferUrgency.scheduled:
        return Colors.green;
    }
  }
}
