import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz;
import '../models/culture.dart';
import 'navigation_service.dart';

/// Service for managing local notifications for culture transfers
class NotificationService {
  static final NotificationService _instance = NotificationService._internal();

  factory NotificationService() {
    return _instance;
  }

  NotificationService._internal();

  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  bool _isInitialized = false;
  bool _permissionsGranted = false;

  /// Initialize the notification service
  Future<bool> initialize() async {
    if (_isInitialized) return _permissionsGranted;

    try {
      // Initialize timezone data
      tz.initializeTimeZones();

      // Android initialization settings
      const AndroidInitializationSettings initializationSettingsAndroid =
          AndroidInitializationSettings('@mipmap/ic_launcher');

      // iOS initialization settings
      const DarwinInitializationSettings initializationSettingsIOS =
          DarwinInitializationSettings(
            requestAlertPermission: true,
            requestBadgePermission: true,
            requestSoundPermission: true,
            onDidReceiveLocalNotification: null,
          );

      // Combined initialization settings
      const InitializationSettings initializationSettings =
          InitializationSettings(
            android: initializationSettingsAndroid,
            iOS: initializationSettingsIOS,
          );

      // Initialize the plugin
      final bool? initialized = await _flutterLocalNotificationsPlugin
          .initialize(
            initializationSettings,
            onDidReceiveNotificationResponse: _onNotificationTapped,
          );

      _isInitialized = initialized ?? false;

      if (_isInitialized) {
        _permissionsGranted = await _requestPermissions();
      }

      return _permissionsGranted;
    } catch (e) {
      debugPrint('Failed to initialize notifications: $e');
      return false;
    }
  }

  /// Request notification permissions
  Future<bool> _requestPermissions() async {
    try {
      if (Platform.isAndroid) {
        final AndroidFlutterLocalNotificationsPlugin? androidImplementation =
            _flutterLocalNotificationsPlugin
                .resolvePlatformSpecificImplementation<
                  AndroidFlutterLocalNotificationsPlugin
                >();

        if (androidImplementation != null) {
          final bool? granted =
              await androidImplementation.requestNotificationsPermission();
          return granted ?? false;
        }
      } else if (Platform.isIOS) {
        final bool? granted = await _flutterLocalNotificationsPlugin
            .resolvePlatformSpecificImplementation<
              IOSFlutterLocalNotificationsPlugin
            >()
            ?.requestPermissions(alert: true, badge: true, sound: true);
        return granted ?? false;
      }
      return true; // Default to true for other platforms
    } catch (e) {
      debugPrint('Failed to request permissions: $e');
      return false;
    }
  }

  /// Handle notification tap
  void _onNotificationTapped(NotificationResponse notificationResponse) {
    final String? payload = notificationResponse.payload;
    if (payload != null) {
      debugPrint('Notification tapped with payload: $payload');

      // Parse culture ID from payload and navigate to culture detail
      try {
        final cultureId = int.parse(payload);
        NavigationService().navigateToCultureDetail(cultureId);
      } catch (e) {
        debugPrint('Failed to parse culture ID from payload: $e');
        // Navigate to culture list as fallback
        NavigationService().navigateToCultureList();
      }
    }
  }

  /// Schedule a transfer reminder notification
  Future<bool> scheduleTransferReminder({
    required int cultureId,
    required String cultureSpecies,
    required DateTime transferDate,
    String? variety,
  }) async {
    if (!_permissionsGranted) {
      debugPrint('Notifications not permitted');
      return false;
    }

    try {
      // Calculate notification time (24 hours before transfer)
      final notificationTime = transferDate.subtract(const Duration(hours: 24));

      // Don't schedule if the notification time is in the past
      if (notificationTime.isBefore(DateTime.now())) {
        debugPrint('Notification time is in the past, skipping');
        return false;
      }

      final tz.TZDateTime scheduledDate = tz.TZDateTime.from(
        notificationTime,
        tz.local,
      );

      // Create notification details
      const AndroidNotificationDetails androidPlatformChannelSpecifics =
          AndroidNotificationDetails(
            'culture_transfer_reminders',
            'Culture Transfer Reminders',
            channelDescription: 'Notifications for upcoming culture transfers',
            importance: Importance.high,
            priority: Priority.high,
            icon: '@mipmap/ic_launcher',
            largeIcon: DrawableResourceAndroidBitmap('@mipmap/ic_launcher'),
          );

      const DarwinNotificationDetails iOSPlatformChannelSpecifics =
          DarwinNotificationDetails(
            presentAlert: true,
            presentBadge: true,
            presentSound: true,
          );

      const NotificationDetails platformChannelSpecifics = NotificationDetails(
        android: androidPlatformChannelSpecifics,
        iOS: iOSPlatformChannelSpecifics,
      );

      // Create notification content
      final String title = 'Culture Transfer Reminder';
      final String body =
          variety != null
              ? 'Transfer reminder for $cultureSpecies ($variety) - Due tomorrow'
              : 'Transfer reminder for $cultureSpecies - Due tomorrow';

      // Schedule the notification
      await _flutterLocalNotificationsPlugin.zonedSchedule(
        cultureId, // Use culture ID as notification ID
        title,
        body,
        scheduledDate,
        platformChannelSpecifics,
        androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
        uiLocalNotificationDateInterpretation:
            UILocalNotificationDateInterpretation.absoluteTime,
        payload: cultureId.toString(),
      );

      debugPrint(
        'Scheduled notification for culture $cultureId at $scheduledDate',
      );
      return true;
    } catch (e) {
      debugPrint('Failed to schedule notification: $e');
      return false;
    }
  }

  /// Cancel a scheduled notification
  Future<bool> cancelNotification(int cultureId) async {
    try {
      await _flutterLocalNotificationsPlugin.cancel(cultureId);
      debugPrint('Cancelled notification for culture $cultureId');
      return true;
    } catch (e) {
      debugPrint('Failed to cancel notification: $e');
      return false;
    }
  }

  /// Cancel all scheduled notifications
  Future<bool> cancelAllNotifications() async {
    try {
      await _flutterLocalNotificationsPlugin.cancelAll();
      debugPrint('Cancelled all notifications');
      return true;
    } catch (e) {
      debugPrint('Failed to cancel all notifications: $e');
      return false;
    }
  }

  /// Get pending notifications
  Future<List<PendingNotificationRequest>> getPendingNotifications() async {
    try {
      return await _flutterLocalNotificationsPlugin
          .pendingNotificationRequests();
    } catch (e) {
      debugPrint('Failed to get pending notifications: $e');
      return [];
    }
  }

  /// Check if notifications are enabled
  bool get isInitialized => _isInitialized;
  bool get hasPermissions => _permissionsGranted;

  /// Schedule notifications for a culture
  Future<bool> scheduleNotificationsForCulture(Culture culture) async {
    if (culture.nextTransferDate == null) return false;

    return await scheduleTransferReminder(
      cultureId: culture.id!,
      cultureSpecies: culture.species,
      transferDate: culture.nextTransferDate!,
      variety: culture.variety,
    );
  }

  /// Reschedule notifications for a culture (cancel old, schedule new)
  Future<bool> rescheduleNotificationsForCulture(Culture culture) async {
    await cancelNotification(culture.id!);
    return await scheduleNotificationsForCulture(culture);
  }
}
