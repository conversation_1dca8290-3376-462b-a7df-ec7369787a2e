import 'package:flutter/material.dart';
import '../screens/dashboard_screen.dart';
import '../screens/culture_list_screen.dart';
import '../screens/upcoming_transfers_screen.dart';
import '../screens/recipe_list_screen.dart';

class MainNavigation extends StatefulWidget {
  const MainNavigation({super.key});

  @override
  State<MainNavigation> createState() => _MainNavigationState();
}

class _MainNavigationState extends State<MainNavigation> {
  int _currentIndex = 0;

  final List<Widget> _screens = [
    const DashboardScreen(),
    const CultureListScreen(),
    const UpcomingTransfersScreen(),
    const RecipeListScreen(),
  ];

  final List<BottomNavigationBarItem> _navigationItems = [
    const BottomNavigationBarItem(
      icon: Icon(Icons.dashboard),
      label: 'Dashboard',
    ),
    const BottomNavigationBarItem(icon: Icon(Icons.science), label: 'Cultures'),
    const BottomNavigationBarItem(
      icon: Icon(Icons.schedule),
      label: 'Transfers',
    ),
    const BottomNavigationBarItem(
      icon: Icon(Icons.menu_book),
      label: 'Recipes',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: IndexedStack(index: _currentIndex, children: _screens),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _currentIndex,
        onTap: _onTabTapped,
        items: _navigationItems,
        type: BottomNavigationBarType.fixed,
        selectedItemColor: Theme.of(context).primaryColor,
        unselectedItemColor: Colors.grey,
      ),
    );
  }

  void _onTabTapped(int index) {
    setState(() {
      _currentIndex = index;
    });
  }
}
