import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:culture_track/screens/culture_detail_screen.dart';
import 'package:culture_track/providers/culture_provider.dart';
import 'package:culture_track/models/culture.dart';

// Mock CultureProvider for testing
class MockCultureProvider extends CultureProvider {
  Culture? _mockCulture;

  void setMockCulture(Culture culture) {
    _mockCulture = culture;
  }

  @override
  Culture? getCultureById(int id) {
    return _mockCulture;
  }

  @override
  Future<void> loadCultures() async {
    // Mock implementation - do nothing
  }

  @override
  Future<bool> addPhotoToCulture(int cultureId, String photoPath) async {
    if (_mockCulture != null) {
      final updatedPhotos = List<String>.from(_mockCulture!.photos)..add(photoPath);
      _mockCulture = _mockCulture!.copyWith(photos: updatedPhotos);
      notifyListeners();
      return true;
    }
    return false;
  }

  @override
  Future<bool> removePhotoFromCulture(int cultureId, String photoPath) async {
    if (_mockCulture != null) {
      final updatedPhotos = List<String>.from(_mockCulture!.photos)..remove(photoPath);
      _mockCulture = _mockCulture!.copyWith(photos: updatedPhotos);
      notifyListeners();
      return true;
    }
    return false;
  }
}

void main() {
  group('CultureDetailScreen Widget Tests', () {
    late MockCultureProvider mockProvider;

    setUp(() {
      mockProvider = MockCultureProvider();
    });

    Widget createTestWidget(String cultureId) {
      return MaterialApp(
        home: ChangeNotifierProvider<CultureProvider>.value(
          value: mockProvider,
          child: CultureDetailScreen(cultureId: cultureId),
        ),
      );
    }

    testWidgets('should show loading indicator initially', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget('1'));
      
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('should show culture not found when culture is null', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget('999'));
      await tester.pumpAndSettle();
      
      expect(find.text('Culture not found'), findsOneWidget);
    });

    testWidgets('should display culture details when culture exists', (WidgetTester tester) async {
      final testCulture = Culture(
        id: 1,
        species: 'Test Species',
        variety: 'Test Variety',
        source: 'Test Source',
        creationDate: DateTime(2024, 1, 1),
        currentStage: CultureStage.initiation,
        status: CultureStatus.active,
      );
      
      mockProvider.setMockCulture(testCulture);
      
      await tester.pumpWidget(createTestWidget('1'));
      await tester.pumpAndSettle();
      
      expect(find.text('Culture ID: 1'), findsOneWidget);
      expect(find.text('Species: Test Species'), findsOneWidget);
      expect(find.text('Variety: Test Variety'), findsOneWidget);
      expect(find.text('Status: Active'), findsOneWidget);
      expect(find.text('Stage: Initiation'), findsOneWidget);
    });

    testWidgets('should show camera icon in app bar', (WidgetTester tester) async {
      final testCulture = Culture(
        id: 1,
        species: 'Test Species',
        creationDate: DateTime(2024, 1, 1),
        currentStage: CultureStage.initiation,
        status: CultureStatus.active,
      );
      
      mockProvider.setMockCulture(testCulture);
      
      await tester.pumpWidget(createTestWidget('1'));
      await tester.pumpAndSettle();
      
      expect(find.byIcon(Icons.camera_alt), findsOneWidget);
    });

    testWidgets('should show empty photos state when no photos', (WidgetTester tester) async {
      final testCulture = Culture(
        id: 1,
        species: 'Test Species',
        creationDate: DateTime(2024, 1, 1),
        currentStage: CultureStage.initiation,
        status: CultureStatus.active,
        photos: [],
      );
      
      mockProvider.setMockCulture(testCulture);
      
      await tester.pumpWidget(createTestWidget('1'));
      await tester.pumpAndSettle();
      
      expect(find.text('No photos yet'), findsOneWidget);
      expect(find.text('Tap the camera icon to add photos'), findsOneWidget);
      expect(find.text('0 photos'), findsOneWidget);
    });

    testWidgets('should show photo count when photos exist', (WidgetTester tester) async {
      final testCulture = Culture(
        id: 1,
        species: 'Test Species',
        creationDate: DateTime(2024, 1, 1),
        currentStage: CultureStage.initiation,
        status: CultureStatus.active,
        photos: ['/test/photo1.jpg', '/test/photo2.jpg'],
      );
      
      mockProvider.setMockCulture(testCulture);
      
      await tester.pumpWidget(createTestWidget('1'));
      await tester.pumpAndSettle();
      
      expect(find.text('2 photos'), findsOneWidget);
    });

    testWidgets('should handle singular photo count correctly', (WidgetTester tester) async {
      final testCulture = Culture(
        id: 1,
        species: 'Test Species',
        creationDate: DateTime(2024, 1, 1),
        currentStage: CultureStage.initiation,
        status: CultureStatus.active,
        photos: ['/test/photo1.jpg'],
      );
      
      mockProvider.setMockCulture(testCulture);
      
      await tester.pumpWidget(createTestWidget('1'));
      await tester.pumpAndSettle();
      
      expect(find.text('1 photo'), findsOneWidget);
    });

    testWidgets('should show transfer schedule information', (WidgetTester tester) async {
      final testCulture = Culture(
        id: 1,
        species: 'Test Species',
        creationDate: DateTime(2024, 1, 1),
        currentStage: CultureStage.initiation,
        status: CultureStatus.active,
        lastTransferDate: DateTime(2024, 1, 15),
        nextTransferDate: DateTime(2024, 2, 1),
      );
      
      mockProvider.setMockCulture(testCulture);
      
      await tester.pumpWidget(createTestWidget('1'));
      await tester.pumpAndSettle();
      
      expect(find.text('Last Transfer: 15/1/2024'), findsOneWidget);
      expect(find.text('Next Transfer: 1/2/2024'), findsOneWidget);
    });
  });
}
