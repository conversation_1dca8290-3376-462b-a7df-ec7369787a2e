import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/culture.dart';
import '../providers/culture_provider.dart';
import '../services/transfer_date_calculator.dart';

/// Screen displaying upcoming culture transfers in a list format
class UpcomingTransfersScreen extends StatefulWidget {
  const UpcomingTransfersScreen({super.key});

  @override
  State<UpcomingTransfersScreen> createState() => _UpcomingTransfersScreenState();
}

class _UpcomingTransfersScreenState extends State<UpcomingTransfersScreen> {
  final TransferDateCalculator _dateCalculator = TransferDateCalculator();
  List<Culture> _upcomingCultures = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadUpcomingTransfers();
  }

  Future<void> _loadUpcomingTransfers() async {
    setState(() {
      _isLoading = true;
    });

    final cultureProvider = Provider.of<CultureProvider>(context, listen: false);
    await cultureProvider.loadCultures();

    // Filter cultures with upcoming transfer dates (next 30 days)
    final now = DateTime.now();
    final thirtyDaysFromNow = now.add(const Duration(days: 30));

    final allCultures = cultureProvider.cultures;
    final upcomingCultures = allCultures.where((culture) {
      if (culture.nextTransferDate == null) return false;
      return culture.nextTransferDate!.isAfter(now) &&
             culture.nextTransferDate!.isBefore(thirtyDaysFromNow);
    }).toList();

    // Sort by transfer date (earliest first)
    upcomingCultures.sort((a, b) => 
        a.nextTransferDate!.compareTo(b.nextTransferDate!));

    setState(() {
      _upcomingCultures = upcomingCultures;
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Upcoming Transfers'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadUpcomingTransfers,
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _upcomingCultures.isEmpty
              ? _buildEmptyState()
              : _buildTransfersList(),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.event_available,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No Upcoming Transfers',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'All cultures are up to date or no transfers scheduled in the next 30 days.',
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.of(context).pushNamed('/add-culture');
            },
            icon: const Icon(Icons.add),
            label: const Text('Add Culture'),
          ),
        ],
      ),
    );
  }

  Widget _buildTransfersList() {
    return RefreshIndicator(
      onRefresh: _loadUpcomingTransfers,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _upcomingCultures.length,
        itemBuilder: (context, index) {
          final culture = _upcomingCultures[index];
          return _buildTransferCard(culture);
        },
      ),
    );
  }

  Widget _buildTransferCard(Culture culture) {
    final urgency = _dateCalculator.getTransferUrgency(culture.nextTransferDate!);
    final daysUntil = _dateCalculator.calculateDaysUntilTransfer(culture.nextTransferDate!);
    
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      child: InkWell(
        onTap: () => _navigateToCultureDetail(culture),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          culture.species,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        if (culture.variety != null)
                          Text(
                            culture.variety!,
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[600],
                            ),
                          ),
                      ],
                    ),
                  ),
                  _buildUrgencyChip(urgency),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Icon(
                    Icons.schedule,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'Due: ${_formatDate(culture.nextTransferDate!)}',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                  const Spacer(),
                  Text(
                    _getDaysUntilText(daysUntil),
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: _getUrgencyColor(urgency),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(
                    Icons.science,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'Stage: ${culture.currentStage.displayName}',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                  const Spacer(),
                  Icon(
                    _getStatusIcon(culture.status),
                    size: 16,
                    color: _getStatusColor(culture.status),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    culture.status.displayName,
                    style: TextStyle(
                      fontSize: 14,
                      color: _getStatusColor(culture.status),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildUrgencyChip(TransferUrgency urgency) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: _getUrgencyColor(urgency).withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _getUrgencyColor(urgency).withOpacity(0.3),
        ),
      ),
      child: Text(
        urgency.displayName,
        style: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          color: _getUrgencyColor(urgency),
        ),
      ),
    );
  }

  Color _getUrgencyColor(TransferUrgency urgency) {
    switch (urgency) {
      case TransferUrgency.overdue:
        return Colors.red;
      case TransferUrgency.urgent:
        return Colors.orange;
      case TransferUrgency.soon:
        return Colors.amber;
      case TransferUrgency.upcoming:
        return Colors.blue;
      case TransferUrgency.scheduled:
        return Colors.green;
    }
  }

  IconData _getStatusIcon(CultureStatus status) {
    switch (status) {
      case CultureStatus.active:
        return Icons.check_circle;
      case CultureStatus.contaminated:
        return Icons.warning;
      case CultureStatus.transferred:
        return Icons.swap_horiz;
      case CultureStatus.completed:
        return Icons.done_all;
      case CultureStatus.failed:
        return Icons.error;
    }
  }

  Color _getStatusColor(CultureStatus status) {
    switch (status) {
      case CultureStatus.active:
        return Colors.green;
      case CultureStatus.contaminated:
        return Colors.red;
      case CultureStatus.transferred:
        return Colors.blue;
      case CultureStatus.completed:
        return Colors.purple;
      case CultureStatus.failed:
        return Colors.grey;
    }
  }

  String _getDaysUntilText(int days) {
    if (days < 0) {
      return '${days.abs()} days overdue';
    } else if (days == 0) {
      return 'Due today';
    } else if (days == 1) {
      return 'Due tomorrow';
    } else {
      return 'In $days days';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _navigateToCultureDetail(Culture culture) {
    Navigator.of(context).pushNamed(
      '/culture-detail',
      arguments: {'cultureId': culture.id.toString()},
    );
  }
}
