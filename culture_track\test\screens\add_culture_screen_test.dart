import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:culture_track/screens/add_culture_screen.dart';
import 'package:culture_track/providers/culture_provider.dart';
import 'package:culture_track/models/culture.dart';

void main() {
  group('AddCultureScreen Tests', () {
    late CultureProvider mockProvider;

    setUp(() {
      mockProvider = CultureProvider();
    });

    Widget createTestWidget() {
      return MaterialApp(
        home: ChangeNotifierProvider<CultureProvider>.value(
          value: mockProvider,
          child: const AddCultureScreen(),
        ),
      );
    }

    testWidgets('displays all required form fields', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Check for all form fields
      expect(find.byType(TextFormField), findsNWidgets(4)); // species, variety, source, notes
      expect(find.text('Species *'), findsOneWidget);
      expect(find.text('Variety (optional)'), findsOneWidget);
      expect(find.text('Source *'), findsOneWidget);
      expect(find.text('Notes (optional)'), findsOneWidget);
      
      // Check for date fields
      expect(find.text('Creation Date *'), findsOneWidget);
      expect(find.text('Next Transfer Date (optional)'), findsOneWidget);
      
      // Check for dropdown
      expect(find.text('Current Stage *'), findsOneWidget);
      expect(find.byType(DropdownButtonFormField<CultureStage>), findsOneWidget);
      
      // Check for save button
      expect(find.text('Save Culture'), findsOneWidget);
    });

    testWidgets('has correct app bar', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.text('Add Culture'), findsOneWidget);
      expect(find.byType(AppBar), findsOneWidget);
    });

    testWidgets('validates required fields', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Try to save without filling required fields
      await tester.tap(find.text('Save Culture'));
      await tester.pump();

      // Should show validation errors
      expect(find.text('Please enter a species'), findsOneWidget);
      expect(find.text('Please enter a source'), findsOneWidget);
    });

    testWidgets('accepts valid input and enables save', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Fill in required fields
      await tester.enterText(
        find.widgetWithText(TextFormField, 'Species *'),
        'Test Species',
      );
      await tester.enterText(
        find.widgetWithText(TextFormField, 'Source *'),
        'Test Source',
      );

      // Pump to update validation
      await tester.pump();

      // Save button should be enabled (not null onPressed)
      final saveButton = tester.widget<ElevatedButton>(
        find.widgetWithText(ElevatedButton, 'Save Culture'),
      );
      expect(saveButton.onPressed, isNotNull);
    });

    testWidgets('shows optional fields correctly', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Optional fields should not be required
      expect(find.text('Variety (optional)'), findsOneWidget);
      expect(find.text('Notes (optional)'), findsOneWidget);
      expect(find.text('Next Transfer Date (optional)'), findsOneWidget);
    });

    testWidgets('stage dropdown shows all stages', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Tap the dropdown to open it
      await tester.tap(find.byType(DropdownButtonFormField<CultureStage>));
      await tester.pump();

      // Should show all culture stages
      expect(find.text('Initiation'), findsOneWidget);
      expect(find.text('Multiplication'), findsOneWidget);
      expect(find.text('Rooting'), findsOneWidget);
      expect(find.text('Acclimatization'), findsOneWidget);
    });

    testWidgets('can select different stages', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Tap the dropdown
      await tester.tap(find.byType(DropdownButtonFormField<CultureStage>));
      await tester.pump();

      // Select multiplication stage
      await tester.tap(find.text('Multiplication').last);
      await tester.pump();

      // Verify selection
      expect(find.text('Multiplication'), findsOneWidget);
    });

    testWidgets('date fields show calendar icon', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Should have calendar icons for date fields
      expect(find.byIcon(Icons.calendar_today), findsNWidgets(2));
    });

    testWidgets('shows proper hints for text fields', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Check for helpful hints
      expect(find.text('e.g., Orchid, Rose, Potato'), findsOneWidget);
      expect(find.text('e.g., Phalaenopsis, Red Beauty'), findsOneWidget);
      expect(find.text('e.g., Mother plant, Seed, Cutting'), findsOneWidget);
      expect(find.text('Any additional information...'), findsOneWidget);
    });

    testWidgets('form is scrollable', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Should have SingleChildScrollView for scrolling
      expect(find.byType(SingleChildScrollView), findsOneWidget);
    });

    testWidgets('save button shows loading state', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Fill required fields
      await tester.enterText(
        find.widgetWithText(TextFormField, 'Species *'),
        'Test Species',
      );
      await tester.enterText(
        find.widgetWithText(TextFormField, 'Source *'),
        'Test Source',
      );

      // Note: Testing actual save functionality would require mocking the provider
      // For now, we just verify the button structure is correct
      final saveButton = find.widgetWithText(ElevatedButton, 'Save Culture');
      expect(saveButton, findsOneWidget);
    });
  });
}
