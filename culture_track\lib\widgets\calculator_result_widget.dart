import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/recipe_calculator_service.dart';

/// Widget for displaying calculated recipe results
class CalculatorResultWidget extends StatelessWidget {
  final Map<String, CalculatedIngredient> calculatedIngredients;
  final double targetVolume;
  final double scaleFactor;

  const CalculatorResultWidget({
    super.key,
    required this.calculatedIngredients,
    required this.targetVolume,
    required this.scaleFactor,
  });

  @override
  Widget build(BuildContext context) {
    if (calculatedIngredients.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header with scale info
        Card(
          color: Theme.of(context).colorScheme.primaryContainer,
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                Icon(
                  Icons.calculate,
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Calculated for ${_formatVolume(targetVolume)}',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onPrimaryContainer,
                        ),
                      ),
                      Text(
                        'Scale factor: ${scaleFactor.toStringAsFixed(2)}x',
                        style: TextStyle(
                          fontSize: 14,
                          color: Theme.of(context).colorScheme.onPrimaryContainer.withValues(alpha: 0.8),
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () => _copyToClipboard(context),
                  icon: Icon(
                    Icons.copy,
                    color: Theme.of(context).colorScheme.onPrimaryContainer,
                  ),
                  tooltip: 'Copy to clipboard',
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 16),

        // Ingredients list
        Card(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  'Calculated Ingredients',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const Divider(height: 1),
              ...calculatedIngredients.entries.map((entry) {
                return _buildIngredientRow(context, entry.key, entry.value);
              }),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildIngredientRow(BuildContext context, String name, CalculatedIngredient ingredient) {
    final isScaled = ingredient.scaleFactor != 1.0;
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Colors.grey.shade200,
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        children: [
          // Ingredient name
          Expanded(
            flex: 2,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 16,
                  ),
                ),
                if (ingredient.notes != null && ingredient.notes!.isNotEmpty)
                  Text(
                    ingredient.notes!,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                      fontStyle: FontStyle.italic,
                    ),
                  ),
              ],
            ),
          ),
          
          // Original amount (if scaled)
          if (isScaled)
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    ingredient.formattedOriginalAmount,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                      decoration: TextDecoration.lineThrough,
                    ),
                  ),
                  const Text(
                    'original',
                    style: TextStyle(
                      fontSize: 10,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
          
          // Arrow (if scaled)
          if (isScaled)
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 8),
              child: Icon(
                Icons.arrow_forward,
                size: 16,
                color: Colors.grey[600],
              ),
            ),
          
          // Scaled amount
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  ingredient.formattedScaledAmount,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: isScaled 
                        ? Theme.of(context).colorScheme.primary
                        : null,
                  ),
                ),
                if (isScaled)
                  Text(
                    '${ingredient.percentageChange >= 0 ? '+' : ''}${ingredient.percentageChange.toStringAsFixed(0)}%',
                    style: TextStyle(
                      fontSize: 10,
                      color: ingredient.percentageChange >= 0 
                          ? Colors.green[600]
                          : Colors.red[600],
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _formatVolume(double volume) {
    if (volume >= 1) {
      return volume == volume.toInt() 
          ? '${volume.toInt()} L'
          : '${volume.toStringAsFixed(1)} L';
    } else {
      final ml = (volume * 1000).toInt();
      return '$ml mL';
    }
  }

  void _copyToClipboard(BuildContext context) {
    final buffer = StringBuffer();
    buffer.writeln('Recipe Calculation for ${_formatVolume(targetVolume)}');
    buffer.writeln('Scale factor: ${scaleFactor.toStringAsFixed(2)}x');
    buffer.writeln('');
    buffer.writeln('Ingredients:');
    
    for (final entry in calculatedIngredients.entries) {
      final name = entry.key;
      final ingredient = entry.value;
      buffer.writeln('• $name: ${ingredient.formattedScaledAmount}');
      if (ingredient.notes != null && ingredient.notes!.isNotEmpty) {
        buffer.writeln('  Notes: ${ingredient.notes}');
      }
    }

    Clipboard.setData(ClipboardData(text: buffer.toString()));
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Recipe calculation copied to clipboard'),
        duration: Duration(seconds: 2),
      ),
    );
  }
}
