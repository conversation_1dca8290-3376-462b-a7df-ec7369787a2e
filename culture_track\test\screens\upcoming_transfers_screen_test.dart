import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:culture_track/screens/upcoming_transfers_screen.dart';
import 'package:culture_track/providers/culture_provider.dart';
import 'package:culture_track/models/culture.dart';

// Mock CultureProvider for testing
class MockCultureProvider extends CultureProvider {
  List<Culture> _mockCultures = [];

  @override
  List<Culture> get cultures => _mockCultures;

  void addMockCulture(Culture culture) {
    _mockCultures.add(culture);
    notifyListeners();
  }

  @override
  Future<void> loadCultures() async {
    // Mock implementation - do nothing
  }
}

void main() {
  group('UpcomingTransfersScreen Tests', () {
    late MockCultureProvider mockCultureProvider;

    setUp(() {
      mockCultureProvider = MockCultureProvider();
    });

    Widget createTestWidget() {
      return MaterialApp(
        home: ChangeNotifierProvider<CultureProvider>.value(
          value: mockCultureProvider,
          child: const UpcomingTransfersScreen(),
        ),
      );
    }

    testWidgets('displays empty state when no upcoming transfers', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      expect(find.text('No Upcoming Transfers'), findsOneWidget);
      expect(
        find.text(
          'All cultures are up to date or no transfers scheduled in the next 30 days.',
        ),
        findsOneWidget,
      );
      expect(find.byIcon(Icons.event_available), findsOneWidget);
    });

    testWidgets('displays app bar with title and refresh button', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      expect(find.text('Upcoming Transfers'), findsOneWidget);
      expect(find.byIcon(Icons.refresh), findsOneWidget);
    });

    testWidgets('displays upcoming transfers when available', (
      WidgetTester tester,
    ) async {
      // Create test cultures with upcoming transfer dates
      final now = DateTime.now();
      final tomorrow = now.add(const Duration(days: 1));
      final nextWeek = now.add(const Duration(days: 7));

      final testCultures = [
        Culture(
          id: 1,
          species: 'Test Species 1',
          variety: 'Variety A',
          creationDate: now.subtract(const Duration(days: 30)),
          currentStage: CultureStage.multiplication,
          status: CultureStatus.active,
          nextTransferDate: tomorrow,
        ),
        Culture(
          id: 2,
          species: 'Test Species 2',
          creationDate: now.subtract(const Duration(days: 20)),
          currentStage: CultureStage.rooting,
          status: CultureStatus.active,
          nextTransferDate: nextWeek,
        ),
      ];

      // Add cultures to provider
      for (final culture in testCultures) {
        mockCultureProvider.addMockCulture(culture);
      }

      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Should display the cultures
      expect(find.text('Test Species 1'), findsOneWidget);
      expect(find.text('Test Species 2'), findsOneWidget);
      expect(find.text('Variety A'), findsOneWidget);
    });

    testWidgets('displays urgency indicators correctly', (
      WidgetTester tester,
    ) async {
      final now = DateTime.now();
      final overdue = now.subtract(const Duration(days: 1));
      final urgent = now.add(const Duration(days: 1));

      final testCultures = [
        Culture(
          id: 1,
          species: 'Overdue Culture',
          creationDate: now.subtract(const Duration(days: 30)),
          currentStage: CultureStage.multiplication,
          status: CultureStatus.active,
          nextTransferDate: overdue,
        ),
        Culture(
          id: 2,
          species: 'Urgent Culture',
          creationDate: now.subtract(const Duration(days: 20)),
          currentStage: CultureStage.rooting,
          status: CultureStatus.active,
          nextTransferDate: urgent,
        ),
      ];

      for (final culture in testCultures) {
        mockCultureProvider.addMockCulture(culture);
      }

      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      expect(find.text('Overdue Culture'), findsOneWidget);
      expect(find.text('Urgent Culture'), findsOneWidget);
    });

    testWidgets('refresh functionality works', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Tap refresh button
      await tester.tap(find.byIcon(Icons.refresh));
      await tester.pumpAndSettle();

      // Should still show empty state
      expect(find.text('No Upcoming Transfers'), findsOneWidget);
    });

    testWidgets('pull to refresh works', (WidgetTester tester) async {
      final now = DateTime.now();
      final tomorrow = now.add(const Duration(days: 1));

      final testCulture = Culture(
        id: 1,
        species: 'Test Species',
        creationDate: now.subtract(const Duration(days: 30)),
        currentStage: CultureStage.multiplication,
        status: CultureStatus.active,
        nextTransferDate: tomorrow,
      );

      mockCultureProvider.addMockCulture(testCulture);

      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Find the RefreshIndicator and trigger pull to refresh
      await tester.fling(find.byType(ListView), const Offset(0, 300), 1000);
      await tester.pump();
      await tester.pump(const Duration(seconds: 1));

      expect(find.text('Test Species'), findsOneWidget);
    });
  });
}
