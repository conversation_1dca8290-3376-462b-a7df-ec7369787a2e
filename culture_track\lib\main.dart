import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'routes/app_routes.dart';
import 'providers/culture_provider.dart';
import 'providers/recipe_provider.dart';
import 'services/navigation_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  runApp(const CultureTrackApp());
}

class CultureTrackApp extends StatefulWidget {
  const CultureTrackApp({super.key});

  @override
  State<CultureTrackApp> createState() => _CultureTrackAppState();
}

class _CultureTrackAppState extends State<CultureTrackApp> {
  @override
  void initState() {
    super.initState();
    _initializeNotifications();
  }

  Future<void> _initializeNotifications() async {
    // Initialize notifications after the first frame
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final cultureProvider = Provider.of<CultureProvider>(
        context,
        listen: false,
      );
      await cultureProvider.initializeNotifications();
    });
  }

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => CultureProvider()),
        ChangeNotifierProvider(create: (_) => RecipeProvider()),
      ],
      child: MaterialApp(
        title: 'CultureTrack',
        navigatorKey: NavigationService().navigatorKey,
        theme: ThemeData(
          colorScheme: ColorScheme.fromSeed(
            seedColor: Colors.green,
            brightness: Brightness.light,
          ),
          useMaterial3: true,
          appBarTheme: const AppBarTheme(centerTitle: true, elevation: 0),
        ),
        initialRoute: AppRoutes.home,
        routes: AppRoutes.routes,
        onGenerateRoute: AppRoutes.onGenerateRoute,
        onUnknownRoute: AppRoutes.onUnknownRoute,
      ),
    );
  }
}
