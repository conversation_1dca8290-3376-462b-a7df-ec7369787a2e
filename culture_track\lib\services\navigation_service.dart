import 'package:flutter/material.dart';

/// Service for managing navigation throughout the app
/// Provides global navigation context for notifications and deep linking
class NavigationService {
  static final NavigationService _instance = NavigationService._internal();
  
  factory NavigationService() {
    return _instance;
  }
  
  NavigationService._internal();

  final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  /// Get the current navigation context
  BuildContext? get context => navigatorKey.currentContext;

  /// Navigate to culture detail screen
  Future<void> navigateToCultureDetail(int cultureId) async {
    final context = this.context;
    if (context != null) {
      await Navigator.of(context).pushNamed(
        '/culture-detail',
        arguments: cultureId.toString(),
      );
    }
  }

  /// Navigate to home screen
  Future<void> navigateToHome() async {
    final context = this.context;
    if (context != null) {
      await Navigator.of(context).pushNamedAndRemoveUntil(
        '/',
        (route) => false,
      );
    }
  }

  /// Navigate to culture list screen
  Future<void> navigateToCultureList() async {
    final context = this.context;
    if (context != null) {
      await Navigator.of(context).pushNamedAndRemoveUntil(
        '/cultures',
        (route) => false,
      );
    }
  }

  /// Show a snackbar message
  void showSnackBar(String message, {Color? backgroundColor}) {
    final context = this.context;
    if (context != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: backgroundColor,
        ),
      );
    }
  }

  /// Show an error message
  void showError(String message) {
    showSnackBar(message, backgroundColor: Colors.red);
  }

  /// Show a success message
  void showSuccess(String message) {
    showSnackBar(message, backgroundColor: Colors.green);
  }
}
