import '../models/culture.dart';
import 'transfer_interval_service.dart';

/// Service for calculating next transfer dates based on stage intervals
class TransferDateCalculator {
  static final TransferDateCalculator _instance = TransferDateCalculator._internal();
  
  factory TransferDateCalculator() {
    return _instance;
  }
  
  TransferDateCalculator._internal();

  final TransferIntervalService _intervalService = TransferIntervalService();

  /// Calculate next transfer date for a new culture
  /// Uses creation date as the base date
  DateTime calculateNextTransferDate({
    required DateTime baseDate,
    required CultureStage currentStage,
    CultureStage? targetStage,
  }) {
    // Use target stage if provided, otherwise use current stage
    final stageForCalculation = targetStage ?? currentStage;
    final intervalDays = _intervalService.getIntervalForStage(stageForCalculation);
    
    return baseDate.add(Duration(days: intervalDays));
  }

  /// Calculate next transfer date for an existing culture after stage change
  /// Uses last transfer date or creation date as base
  DateTime calculateNextTransferDateForUpdate({
    required Culture culture,
    required CultureStage newStage,
    DateTime? customBaseDate,
  }) {
    // Use custom base date if provided, otherwise use last transfer date or creation date
    final baseDate = customBaseDate ?? 
                    culture.lastTransferDate ?? 
                    culture.creationDate;
    
    return calculateNextTransferDate(
      baseDate: baseDate,
      currentStage: newStage,
    );
  }

  /// Calculate next transfer date with custom interval
  DateTime calculateNextTransferDateWithInterval({
    required DateTime baseDate,
    required int intervalDays,
  }) {
    if (intervalDays < 1 || intervalDays > 365) {
      throw ArgumentError('Interval must be between 1 and 365 days');
    }
    
    return baseDate.add(Duration(days: intervalDays));
  }

  /// Calculate multiple future transfer dates for planning
  List<DateTime> calculateFutureTransferDates({
    required DateTime startDate,
    required List<CultureStage> stageSequence,
    int maxDates = 5,
  }) {
    final List<DateTime> dates = [];
    DateTime currentDate = startDate;
    
    for (int i = 0; i < stageSequence.length && dates.length < maxDates; i++) {
      final stage = stageSequence[i];
      final nextDate = calculateNextTransferDate(
        baseDate: currentDate,
        currentStage: stage,
      );
      dates.add(nextDate);
      currentDate = nextDate;
    }
    
    return dates;
  }

  /// Get typical stage progression sequence
  List<CultureStage> getTypicalStageSequence([CultureStage? startingStage]) {
    final start = startingStage ?? CultureStage.initiation;
    final allStages = CultureStage.values;
    final startIndex = allStages.indexOf(start);
    
    return allStages.sublist(startIndex);
  }

  /// Calculate days until next transfer
  int calculateDaysUntilTransfer(DateTime nextTransferDate) {
    final now = DateTime.now();
    final difference = nextTransferDate.difference(now);
    return difference.inDays;
  }

  /// Check if transfer is overdue
  bool isTransferOverdue(DateTime nextTransferDate) {
    return DateTime.now().isAfter(nextTransferDate);
  }

  /// Check if transfer is due soon (within specified days)
  bool isTransferDueSoon(DateTime nextTransferDate, {int withinDays = 3}) {
    final daysUntil = calculateDaysUntilTransfer(nextTransferDate);
    return daysUntil <= withinDays && daysUntil >= 0;
  }

  /// Get transfer urgency level
  TransferUrgency getTransferUrgency(DateTime nextTransferDate) {
    final daysUntil = calculateDaysUntilTransfer(nextTransferDate);
    
    if (daysUntil < 0) {
      return TransferUrgency.overdue;
    } else if (daysUntil <= 1) {
      return TransferUrgency.urgent;
    } else if (daysUntil <= 3) {
      return TransferUrgency.soon;
    } else if (daysUntil <= 7) {
      return TransferUrgency.upcoming;
    } else {
      return TransferUrgency.scheduled;
    }
  }

  /// Calculate optimal transfer date considering weekends
  /// Adjusts date to avoid weekends if preferred
  DateTime calculateOptimalTransferDate({
    required DateTime baseDate,
    required CultureStage stage,
    bool avoidWeekends = false,
  }) {
    DateTime calculatedDate = calculateNextTransferDate(
      baseDate: baseDate,
      currentStage: stage,
    );

    if (!avoidWeekends) {
      return calculatedDate;
    }

    // Adjust for weekends (move to Monday if falls on weekend)
    while (calculatedDate.weekday == DateTime.saturday || 
           calculatedDate.weekday == DateTime.sunday) {
      calculatedDate = calculatedDate.add(const Duration(days: 1));
    }

    return calculatedDate;
  }

  /// Get interval service for configuration
  TransferIntervalService get intervalService => _intervalService;
}

/// Transfer urgency levels
enum TransferUrgency {
  overdue,    // Past due date
  urgent,     // Due today or tomorrow
  soon,       // Due within 3 days
  upcoming,   // Due within a week
  scheduled,  // Future scheduled date
}

/// Extension for TransferUrgency display
extension TransferUrgencyExtension on TransferUrgency {
  String get displayName {
    switch (this) {
      case TransferUrgency.overdue:
        return 'Overdue';
      case TransferUrgency.urgent:
        return 'Urgent';
      case TransferUrgency.soon:
        return 'Due Soon';
      case TransferUrgency.upcoming:
        return 'Upcoming';
      case TransferUrgency.scheduled:
        return 'Scheduled';
    }
  }

  String get colorHex {
    switch (this) {
      case TransferUrgency.overdue:
        return '#F44336'; // Red
      case TransferUrgency.urgent:
        return '#FF9800'; // Orange
      case TransferUrgency.soon:
        return '#FFC107'; // Amber
      case TransferUrgency.upcoming:
        return '#2196F3'; // Blue
      case TransferUrgency.scheduled:
        return '#4CAF50'; // Green
    }
  }
}
