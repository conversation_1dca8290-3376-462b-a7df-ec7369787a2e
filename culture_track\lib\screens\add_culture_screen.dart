import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/culture_provider.dart';
import '../models/culture.dart';

class AddCultureScreen extends StatefulWidget {
  const AddCultureScreen({super.key});

  @override
  State<AddCultureScreen> createState() => _AddCultureScreenState();
}

class _AddCultureScreenState extends State<AddCultureScreen> {
  final _formKey = GlobalKey<FormState>();
  final _speciesController = TextEditingController();
  final _varietyController = TextEditingController();
  final _sourceController = TextEditingController();
  final _notesController = TextEditingController();

  DateTime _creationDate = DateTime.now();
  CultureStage _currentStage = CultureStage.initiation;
  DateTime? _nextTransferDate;
  bool _isLoading = false;

  @override
  void dispose() {
    _speciesController.dispose();
    _varietyController.dispose();
    _sourceController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Add Culture'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          TextButton(
            onPressed: _saveCulture,
            child: const Text('Save', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Species field
              TextFormField(
                controller: _speciesController,
                decoration: const InputDecoration(
                  labelText: 'Species *',
                  hintText: 'e.g., Orchid, Rose, Potato',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a species';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Variety field
              TextFormField(
                controller: _varietyController,
                decoration: const InputDecoration(
                  labelText: 'Variety (optional)',
                  hintText: 'e.g., Phalaenopsis, Red Beauty',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),

              // Source field
              TextFormField(
                controller: _sourceController,
                decoration: const InputDecoration(
                  labelText: 'Source *',
                  hintText: 'e.g., Mother plant, Seed, Cutting',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a source';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Creation date field
              _buildDateField(
                label: 'Creation Date *',
                date: _creationDate,
                onTap: () => _selectCreationDate(context),
              ),
              const SizedBox(height: 16),

              // Current stage dropdown
              _buildStageDropdown(),
              const SizedBox(height: 16),

              // Next transfer date field
              _buildDateField(
                label: 'Next Transfer Date (optional)',
                date: _nextTransferDate,
                onTap: () => _selectNextTransferDate(context),
                isOptional: true,
              ),
              const SizedBox(height: 16),

              // Notes field
              TextFormField(
                controller: _notesController,
                decoration: const InputDecoration(
                  labelText: 'Notes (optional)',
                  hintText: 'Any additional information...',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 32),

              // Save button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _saveCulture,
                  child:
                      _isLoading
                          ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                          : const Text('Save Culture'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build date field widget
  Widget _buildDateField({
    required String label,
    required DateTime? date,
    required VoidCallback onTap,
    bool isOptional = false,
  }) {
    return InkWell(
      onTap: onTap,
      child: InputDecorator(
        decoration: InputDecoration(
          labelText: label,
          border: const OutlineInputBorder(),
          suffixIcon: const Icon(Icons.calendar_today),
        ),
        child: Text(
          date != null
              ? '${date.day}/${date.month}/${date.year}'
              : isOptional
              ? 'Tap to select date'
              : 'Select date',
          style: TextStyle(color: date != null ? null : Colors.grey.shade600),
        ),
      ),
    );
  }

  /// Build stage dropdown widget
  Widget _buildStageDropdown() {
    return DropdownButtonFormField<CultureStage>(
      value: _currentStage,
      decoration: const InputDecoration(
        labelText: 'Current Stage *',
        border: OutlineInputBorder(),
      ),
      items:
          CultureStage.values.map((stage) {
            return DropdownMenuItem(
              value: stage,
              child: Text(stage.displayName),
            );
          }).toList(),
      onChanged: (CultureStage? newStage) {
        if (newStage != null) {
          setState(() {
            _currentStage = newStage;
          });
        }
      },
      validator: (value) {
        if (value == null) {
          return 'Please select a stage';
        }
        return null;
      },
    );
  }

  /// Select creation date
  Future<void> _selectCreationDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _creationDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      helpText: 'Select Creation Date',
    );
    if (picked != null && picked != _creationDate) {
      setState(() {
        _creationDate = picked;
      });
    }
  }

  /// Select next transfer date
  Future<void> _selectNextTransferDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate:
          _nextTransferDate ?? DateTime.now().add(const Duration(days: 30)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      helpText: 'Select Next Transfer Date',
    );
    if (picked != null) {
      setState(() {
        _nextTransferDate = picked;
      });
    }
  }

  /// Save culture to database
  Future<void> _saveCulture() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      try {
        final culture = Culture(
          species: _speciesController.text.trim(),
          variety:
              _varietyController.text.trim().isEmpty
                  ? null
                  : _varietyController.text.trim(),
          source:
              _sourceController.text.trim().isEmpty
                  ? null
                  : _sourceController.text.trim(),
          creationDate: _creationDate,
          currentStage: _currentStage,
          status: CultureStatus.active,
          nextTransferDate: _nextTransferDate,
          notes:
              _notesController.text.trim().isEmpty
                  ? null
                  : _notesController.text.trim(),
        );

        final cultureProvider = context.read<CultureProvider>();
        final savedCulture = await cultureProvider.addCulture(culture);

        if (savedCulture != null) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Culture saved successfully!'),
                backgroundColor: Colors.green,
              ),
            );
            Navigator.pop(context);
          }
        } else {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  cultureProvider.error ?? 'Failed to save culture',
                ),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error: $e'), backgroundColor: Colors.red),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }
}
