import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:culture_track/screens/dashboard_screen.dart';
import 'package:culture_track/providers/culture_provider.dart';
import 'package:culture_track/providers/recipe_provider.dart';
import 'package:culture_track/models/culture.dart';

// Mock CultureProvider for testing
class MockCultureProvider extends CultureProvider {
  List<Culture> _mockCultures = [];

  @override
  List<Culture> get cultures => _mockCultures;

  void addMockCulture(Culture culture) {
    _mockCultures.add(culture);
    notifyListeners();
  }

  @override
  Future<void> loadCultures() async {
    // Mock implementation - do nothing
  }
}

void main() {
  group('DashboardScreen Tests', () {
    late MockCultureProvider mockCultureProvider;
    late RecipeProvider mockRecipeProvider;

    setUp(() {
      mockCultureProvider = MockCultureProvider();
      mockRecipeProvider = RecipeProvider();
    });

    Widget createTestWidget() {
      return MaterialApp(
        routes: {
          '/add-culture':
              (context) => const Scaffold(body: Text('Add Culture Screen')),
          '/add-recipe':
              (context) => const Scaffold(body: Text('Add Recipe Screen')),
        },
        home: MultiProvider(
          providers: [
            ChangeNotifierProvider<CultureProvider>.value(
              value: mockCultureProvider,
            ),
            ChangeNotifierProvider<RecipeProvider>.value(
              value: mockRecipeProvider,
            ),
          ],
          child: const DashboardScreen(),
        ),
      );
    }

    testWidgets('displays welcome message and title', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      expect(find.text('Dashboard'), findsOneWidget);
      expect(find.text('Welcome to CultureTrack'), findsOneWidget);
      expect(
        find.text('Your tissue culture management dashboard'),
        findsOneWidget,
      );
    });

    testWidgets('displays quick stats with zero values initially', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      expect(find.text('Quick Stats'), findsOneWidget);
      expect(find.text('Active Cultures'), findsOneWidget);
      expect(find.text('Pending Transfers'), findsOneWidget);
      expect(find.text('Recipes'), findsOneWidget);
      expect(
        find.text('0'),
        findsAtLeastNWidgets(2),
      ); // At least 2 zeros for cultures and transfers
    });

    testWidgets('displays upcoming transfers section', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      expect(find.text('Upcoming Transfers'), findsOneWidget);
      expect(find.text('View All'), findsOneWidget);
      expect(
        find.text('No upcoming transfers in the next 30 days'),
        findsOneWidget,
      );
    });

    testWidgets('displays quick actions', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      expect(find.text('Quick Actions'), findsOneWidget);
      expect(find.text('Add Culture'), findsOneWidget);
      expect(find.text('View Transfers'), findsOneWidget);
      expect(find.text('Add Recipe'), findsOneWidget);
    });

    testWidgets('quick action buttons navigate correctly', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Test Add Culture navigation
      await tester.tap(find.text('Add Culture'));
      await tester.pumpAndSettle();
      expect(find.text('Add Culture Screen'), findsOneWidget);

      // Go back to dashboard
      await tester.pageBack();
      await tester.pumpAndSettle();

      // Test Add Recipe navigation
      await tester.tap(find.text('Add Recipe'));
      await tester.pumpAndSettle();
      expect(find.text('Add Recipe Screen'), findsOneWidget);
    });

    testWidgets('updates stats when cultures are added', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Initially should show 0 active cultures
      expect(find.text('0'), findsAtLeastNWidgets(2));

      // Add some test cultures
      final now = DateTime.now();
      final testCultures = [
        Culture(
          id: 1,
          species: 'Test Species 1',
          creationDate: now,
          currentStage: CultureStage.multiplication,
          status: CultureStatus.active,
          nextTransferDate: now.add(const Duration(days: 5)),
        ),
        Culture(
          id: 2,
          species: 'Test Species 2',
          creationDate: now,
          currentStage: CultureStage.rooting,
          status: CultureStatus.active,
          nextTransferDate: now.add(const Duration(days: 10)),
        ),
      ];

      for (final culture in testCultures) {
        mockCultureProvider.addMockCulture(culture);
      }

      await tester.pumpAndSettle();

      // Should now show 2 active cultures and 2 pending transfers
      expect(find.text('2'), findsAtLeastNWidgets(2));
    });

    testWidgets('shows upcoming transfers when available', (
      WidgetTester tester,
    ) async {
      final now = DateTime.now();
      final tomorrow = now.add(const Duration(days: 1));

      final testCulture = Culture(
        id: 1,
        species: 'Urgent Culture',
        variety: 'Test Variety',
        creationDate: now.subtract(const Duration(days: 30)),
        currentStage: CultureStage.multiplication,
        status: CultureStatus.active,
        nextTransferDate: tomorrow,
      );

      mockCultureProvider.addMockCulture(testCulture);

      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Should show the culture in upcoming transfers
      expect(find.text('Urgent Culture'), findsOneWidget);
      expect(find.text('Test Variety'), findsOneWidget);
    });

    testWidgets('limits upcoming transfers display to 3 items', (
      WidgetTester tester,
    ) async {
      final now = DateTime.now();

      // Add 5 cultures with upcoming transfers
      for (int i = 1; i <= 5; i++) {
        final culture = Culture(
          id: i,
          species: 'Culture $i',
          creationDate: now.subtract(const Duration(days: 30)),
          currentStage: CultureStage.multiplication,
          status: CultureStatus.active,
          nextTransferDate: now.add(Duration(days: i)),
        );
        mockCultureProvider.addMockCulture(culture);
      }

      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Should only show first 3 cultures (sorted by date)
      expect(find.text('Culture 1'), findsOneWidget);
      expect(find.text('Culture 2'), findsOneWidget);
      expect(find.text('Culture 3'), findsOneWidget);
      expect(find.text('Culture 4'), findsNothing);
      expect(find.text('Culture 5'), findsNothing);
    });
  });
}
