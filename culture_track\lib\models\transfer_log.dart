import 'dart:convert';

/// Represents a transfer log entry in the system
/// Maps to the transfer_logs table in the database
class TransferLog {
  final int? id;
  final int cultureId;
  final String? fromStage;
  final String toStage;
  final DateTime transferDate;
  final String? notes;
  final List<String> photos;
  final bool?
  successIndicator; // null = pending, true = success, false = failed
  final DateTime createdAt;

  TransferLog({
    this.id,
    required this.cultureId,
    this.fromStage,
    required this.toStage,
    required this.transferDate,
    this.notes,
    this.photos = const [],
    this.successIndicator,
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();

  /// Create TransferLog from database map
  factory TransferLog.fromMap(Map<String, dynamic> map) {
    return TransferLog(
      id: map['id'] as int?,
      cultureId: map['culture_id'] as int,
      fromStage: map['from_stage'] as String?,
      toStage: map['to_stage'] as String,
      transferDate: DateTime.fromMillisecondsSinceEpoch(
        (map['transfer_date'] as int) * 1000,
      ),
      notes: map['notes'] as String?,
      photos: _parsePhotos(map['photos'] as String?),
      successIndicator:
          map['success_indicator'] != null
              ? (map['success_indicator'] as int) == 1
              : null,
      createdAt: DateTime.fromMillisecondsSinceEpoch(
        (map['created_at'] as int) * 1000,
      ),
    );
  }

  /// Convert TransferLog to database map
  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'culture_id': cultureId,
      'from_stage': fromStage,
      'to_stage': toStage,
      'transfer_date': transferDate.millisecondsSinceEpoch ~/ 1000,
      'notes': notes,
      'photos': _encodePhotos(photos),
      'success_indicator':
          successIndicator != null ? (successIndicator! ? 1 : 0) : null,
      'created_at': createdAt.millisecondsSinceEpoch ~/ 1000,
    };
  }

  /// Create a copy of TransferLog with updated fields
  TransferLog copyWith({
    int? id,
    int? cultureId,
    String? fromStage,
    String? toStage,
    DateTime? transferDate,
    String? notes,
    List<String>? photos,
    bool? successIndicator,
    DateTime? createdAt,
  }) {
    return TransferLog(
      id: id ?? this.id,
      cultureId: cultureId ?? this.cultureId,
      fromStage: fromStage ?? this.fromStage,
      toStage: toStage ?? this.toStage,
      transferDate: transferDate ?? this.transferDate,
      notes: notes ?? this.notes,
      photos: photos ?? this.photos,
      successIndicator: successIndicator ?? this.successIndicator,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  /// Parse photos JSON string to List of String
  static List<String> _parsePhotos(String? photosJson) {
    if (photosJson == null || photosJson.isEmpty) return [];
    try {
      final List<dynamic> photosList = jsonDecode(photosJson);
      return photosList.cast<String>();
    } catch (e) {
      return [];
    }
  }

  /// Encode photos List of String to JSON string
  static String? _encodePhotos(List<String> photos) {
    if (photos.isEmpty) return null;
    return jsonEncode(photos);
  }

  /// Get display name for from stage
  String get fromStageDisplayName {
    if (fromStage == null) return 'Initial';
    return _getStageDisplayName(fromStage!);
  }

  /// Get display name for to stage
  String get toStageDisplayName {
    return _getStageDisplayName(toStage);
  }

  /// Get stage display name from value
  String _getStageDisplayName(String stage) {
    switch (stage) {
      case 'initiation':
        return 'Initiation';
      case 'multiplication':
        return 'Multiplication';
      case 'rooting':
        return 'Rooting';
      case 'acclimatization':
        return 'Acclimatization';
      default:
        return stage;
    }
  }

  /// Get success indicator display text
  String get successDisplayText {
    if (successIndicator == null) return 'Pending';
    return successIndicator! ? 'Success' : 'Failed';
  }

  /// Get success indicator color
  String get successColorHex {
    if (successIndicator == null) return '#FFA500'; // Orange for pending
    return successIndicator!
        ? '#4CAF50'
        : '#F44336'; // Green for success, Red for failed
  }

  @override
  String toString() {
    return 'TransferLog{id: $id, cultureId: $cultureId, fromStage: $fromStage, toStage: $toStage, transferDate: $transferDate, successIndicator: $successIndicator}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TransferLog &&
        other.id == id &&
        other.cultureId == cultureId &&
        other.fromStage == fromStage &&
        other.toStage == toStage &&
        other.transferDate == transferDate;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        cultureId.hashCode ^
        fromStage.hashCode ^
        toStage.hashCode ^
        transferDate.hashCode;
  }
}
