import '../models/culture.dart';

/// Service for managing transfer interval configurations
/// Provides default intervals for each culture stage
class TransferIntervalService {
  static final TransferIntervalService _instance =
      TransferIntervalService._internal();

  factory TransferIntervalService() {
    return _instance;
  }

  TransferIntervalService._internal();

  /// Default transfer intervals in days for each stage
  static const Map<CultureStage, int> _defaultIntervals = {
    CultureStage.initiation: 14, // 2 weeks
    CultureStage.multiplication: 21, // 3 weeks
    CultureStage.rooting: 28, // 4 weeks
    CultureStage.acclimatization: 35, // 5 weeks
  };

  /// Custom intervals (can be modified by user preferences)
  final Map<CultureStage, int> _customIntervals = {};

  /// Get transfer interval for a specific stage
  int getIntervalForStage(CultureStage stage) {
    return _customIntervals[stage] ?? _defaultIntervals[stage] ?? 14;
  }

  /// Set custom interval for a stage
  void setIntervalForStage(CultureStage stage, int days) {
    if (days < 1 || days > 365) {
      throw ArgumentError('Transfer interval must be between 1 and 365 days');
    }
    _customIntervals[stage] = days;
  }

  /// Reset interval for a stage to default
  void resetIntervalForStage(CultureStage stage) {
    _customIntervals.remove(stage);
  }

  /// Reset all intervals to defaults
  void resetAllIntervals() {
    _customIntervals.clear();
  }

  /// Get all current intervals (custom + defaults)
  Map<CultureStage, int> getAllIntervals() {
    final Map<CultureStage, int> allIntervals = {};
    for (final stage in CultureStage.values) {
      allIntervals[stage] = getIntervalForStage(stage);
    }
    return allIntervals;
  }

  /// Get default interval for a stage
  int getDefaultIntervalForStage(CultureStage stage) {
    return _defaultIntervals[stage] ?? 14;
  }

  /// Check if a stage has custom interval
  bool hasCustomInterval(CultureStage stage) {
    return _customIntervals.containsKey(stage);
  }

  /// Load custom intervals from preferences (future implementation)
  Future<void> loadCustomIntervals() async {
    // TODO: Load from SharedPreferences or database
    // For now, use defaults
  }

  /// Save custom intervals to preferences (future implementation)
  Future<void> saveCustomIntervals() async {
    // TODO: Save to SharedPreferences or database
    // For now, just keep in memory
  }

  /// Get interval display text
  String getIntervalDisplayText(CultureStage stage) {
    final days = getIntervalForStage(stage);
    if (days == 7) {
      return '1 week';
    } else if (days == 14) {
      return '2 weeks';
    } else if (days == 21) {
      return '3 weeks';
    } else if (days == 28) {
      return '4 weeks';
    } else if (days == 35) {
      return '5 weeks';
    } else if (days % 7 == 0) {
      return '${days ~/ 7} weeks';
    } else {
      return '$days days';
    }
  }

  /// Get recommended intervals based on stage characteristics
  static Map<CultureStage, String> getStageRecommendations() {
    return {
      CultureStage.initiation:
          'Initial establishment phase - frequent monitoring needed',
      CultureStage.multiplication:
          'Active growth phase - regular transfers for multiplication',
      CultureStage.rooting:
          'Root development phase - longer intervals for root formation',
      CultureStage.acclimatization:
          'Final preparation phase - extended intervals for hardening',
    };
  }
}
